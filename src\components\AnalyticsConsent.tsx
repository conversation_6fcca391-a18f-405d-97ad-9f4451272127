'use client'

import { useState, useEffect } from 'react';
import { useAnalyticsConsent } from './GoogleAnalytics';
import { AnalyticsConsent } from '@/types/analytics.types';

interface AnalyticsConsentBannerProps {
  onAccept?: () => void;
  onDecline?: () => void;
}

export function AnalyticsConsentBanner({ onAccept, onDecline }: AnalyticsConsentBannerProps) {
  const [showBanner, setShowBanner] = useState(false);
  const { getConsent, updateConsent, revokeConsent } = useAnalyticsConsent();

  useEffect(() => {
    // Check if user has already made a consent decision
    const existingConsent = getConsent();
    if (!existingConsent) {
      setShowBanner(true);
    }
  }, [getConsent]);

  const handleAccept = () => {
    const consent: AnalyticsConsent = {
      analytics: true,
      marketing: false, // Keep marketing disabled by default
      functional: true,
      timestamp: new Date(),
      version: '1.0',
    };

    updateConsent(consent);
    setShowBanner(false);
    onAccept?.();
  };

  const handleDecline = () => {
    revokeConsent();
    setShowBanner(false);
    onDecline?.();
  };

  if (!showBanner) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-background-darker/95 backdrop-blur-sm border-t border-gray-800 p-4 shadow-lg">
      <div className="container mx-auto max-w-4xl">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-text mb-2">
              🍪 Privacy & Analytics
            </h3>
            <p className="text-text-secondary text-sm leading-relaxed">
              We use analytics to improve your SoloQuest experience. This helps us understand how you use the app 
              and identify areas for enhancement. We respect your privacy and only collect anonymized usage data.
            </p>
            <p className="text-text-secondary text-xs mt-2">
              You can change these preferences anytime in your profile settings.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2 min-w-fit">
            <button
              onClick={handleDecline}
              className="px-4 py-2 text-sm font-medium text-text-secondary hover:text-text border border-gray-600 rounded-lg hover:bg-gray-800/50 transition-colors"
            >
              Decline
            </button>
            <button
              onClick={handleAccept}
              className="px-4 py-2 text-sm font-medium bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Accept Analytics
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

interface AnalyticsSettingsProps {
  className?: string;
}

export function AnalyticsSettings({ className = '' }: AnalyticsSettingsProps) {
  const [consent, setConsent] = useState<AnalyticsConsent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { getConsent, updateConsent, revokeConsent } = useAnalyticsConsent();

  useEffect(() => {
    const existingConsent = getConsent();
    setConsent(existingConsent);
    setIsLoading(false);
  }, [getConsent]);

  const handleToggleAnalytics = (enabled: boolean) => {
    if (enabled) {
      const newConsent: AnalyticsConsent = {
        analytics: true,
        marketing: false,
        functional: true,
        timestamp: new Date(),
        version: '1.0',
      };
      updateConsent(newConsent);
      setConsent(newConsent);
    } else {
      const revokedConsent = revokeConsent();
      setConsent(revokedConsent);
    }
  };

  const handleToggleMarketing = (enabled: boolean) => {
    if (!consent) return;

    const newConsent: AnalyticsConsent = {
      ...consent,
      marketing: enabled,
      timestamp: new Date(),
    };
    updateConsent(newConsent);
    setConsent(newConsent);
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-700 rounded w-1/3 mb-4"></div>
        <div className="space-y-3">
          <div className="h-12 bg-gray-700 rounded"></div>
          <div className="h-12 bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h3 className="text-lg font-semibold text-text mb-2">Privacy Settings</h3>
        <p className="text-text-secondary text-sm">
          Control how we collect and use your data to improve SoloQuest.
        </p>
      </div>

      <div className="space-y-4">
        {/* Analytics Toggle */}
        <div className="flex items-center justify-between p-4 bg-background-darker rounded-lg border border-gray-800">
          <div className="flex-1">
            <h4 className="font-medium text-text">Analytics & Usage Data</h4>
            <p className="text-sm text-text-secondary mt-1">
              Help us improve SoloQuest by sharing anonymous usage statistics and performance data.
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer ml-4">
            <input
              type="checkbox"
              checked={consent?.analytics ?? false}
              onChange={(e) => handleToggleAnalytics(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
          </label>
        </div>

        {/* Marketing Toggle */}
        <div className="flex items-center justify-between p-4 bg-background-darker rounded-lg border border-gray-800">
          <div className="flex-1">
            <h4 className="font-medium text-text">Marketing & Personalization</h4>
            <p className="text-sm text-text-secondary mt-1">
              Allow personalized content and marketing communications based on your usage patterns.
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer ml-4">
            <input
              type="checkbox"
              checked={consent?.marketing ?? false}
              onChange={(e) => handleToggleMarketing(e.target.checked)}
              disabled={!consent?.analytics}
              className="sr-only peer disabled:cursor-not-allowed"
            />
            <div className={`w-11 h-6 ${consent?.analytics ? 'bg-gray-600' : 'bg-gray-700'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary ${!consent?.analytics ? 'opacity-50 cursor-not-allowed' : ''}`}></div>
          </label>
        </div>

        {/* Functional Cookies Info */}
        <div className="flex items-center justify-between p-4 bg-background-darker rounded-lg border border-gray-800">
          <div className="flex-1">
            <h4 className="font-medium text-text">Essential Functionality</h4>
            <p className="text-sm text-text-secondary mt-1">
              Required for basic app functionality, authentication, and user preferences. Cannot be disabled.
            </p>
          </div>
          <div className="ml-4">
            <div className="w-11 h-6 bg-primary rounded-full relative">
              <div className="absolute top-[2px] right-[2px] bg-white rounded-full h-5 w-5"></div>
            </div>
          </div>
        </div>
      </div>

      {consent && (
        <div className="text-xs text-text-secondary bg-background-darker p-3 rounded border border-gray-800">
          <p>
            <strong>Last updated:</strong> {consent.timestamp.toLocaleDateString()} at {consent.timestamp.toLocaleTimeString()}
          </p>
          <p className="mt-1">
            <strong>Version:</strong> {consent.version}
          </p>
        </div>
      )}

      <div className="text-xs text-text-secondary">
        <p>
          For more information about how we handle your data, please read our{' '}
          <a href="/privacy" className="text-primary hover:underline">
            Privacy Policy
          </a>
          .
        </p>
      </div>
    </div>
  );
}
