import { SubscriptionTier } from './schema.types';

// Base analytics event interface
export interface BaseAnalyticsEvent {
  event_category: string;
  event_label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

// User authentication events
export interface AuthEvent extends BaseAnalyticsEvent {
  event_category: 'auth';
  auth_method?: 'google' | 'email';
  user_id?: string;
}

export interface LoginEvent extends AuthEvent {
  event_label: 'login_success' | 'login_failed';
}

export interface SignupEvent extends AuthEvent {
  event_label: 'signup_success' | 'signup_failed';
  registration_method?: 'google';
}

export interface LogoutEvent extends AuthEvent {
  event_label: 'logout';
}

// Subscription system events
export interface SubscriptionEvent extends BaseAnalyticsEvent {
  event_category: 'subscription';
  subscription_tier?: SubscriptionTier;
  previous_tier?: SubscriptionTier;
  user_id?: string;
}

export interface SubscriptionChangeEvent extends SubscriptionEvent {
  event_label: 'tier_upgrade' | 'tier_downgrade' | 'tier_change';
  previous_tier: SubscriptionTier;
  subscription_tier: SubscriptionTier;
}

export interface UsageLimitEvent extends SubscriptionEvent {
  event_label: 'limit_reached' | 'limit_warning' | 'limit_exceeded';
  limit_type: 'messages' | 'worlds';
  current_usage: number;
  limit_value: number;
  percentage_used: number;
}

export interface UpgradePromptEvent extends SubscriptionEvent {
  event_label: 'upgrade_prompt_shown' | 'upgrade_prompt_clicked' | 'upgrade_prompt_dismissed';
  prompt_location: string;
  suggested_tier?: SubscriptionTier;
}

// World and quest events
export interface WorldEvent extends BaseAnalyticsEvent {
  event_category: 'world';
  world_id?: string;
  user_id?: string;
}

export interface WorldCreationEvent extends WorldEvent {
  event_label: 'world_created' | 'world_creation_started' | 'world_creation_cancelled';
  world_type?: string;
  creation_method?: 'manual' | 'ai_assisted';
}

export interface WorldInteractionEvent extends WorldEvent {
  event_label: 'world_viewed' | 'world_edited' | 'world_deleted' | 'world_shared';
  world_id: string;
  interaction_duration?: number;
}

// Character events
export interface CharacterEvent extends BaseAnalyticsEvent {
  event_category: 'character';
  character_id?: string;
  world_id?: string;
  user_id?: string;
}

export interface CharacterCreationEvent extends CharacterEvent {
  event_label: 'character_created' | 'character_creation_started' | 'character_creation_cancelled';
  character_class?: string;
  character_race?: string;
  creation_method?: 'manual' | 'ai_assisted';
}

export interface CharacterInteractionEvent extends CharacterEvent {
  event_label: 'character_viewed' | 'character_edited' | 'character_deleted';
  character_id: string;
}

// Quest and campaign events
export interface QuestEvent extends BaseAnalyticsEvent {
  event_category: 'quest';
  quest_id?: string;
  world_id?: string;
  character_id?: string;
  user_id?: string;
}

export interface QuestProgressEvent extends QuestEvent {
  event_label: 'quest_started' | 'quest_completed' | 'quest_abandoned';
  quest_type?: string;
  completion_time?: number;
  success_rate?: number;
}

export interface CampaignEvent extends BaseAnalyticsEvent {
  event_category: 'campaign';
  campaign_id?: string;
  world_id?: string;
  user_id?: string;
}

export interface CampaignInteractionEvent extends CampaignEvent {
  event_label: 'campaign_started' | 'campaign_continued' | 'campaign_paused' | 'campaign_completed';
  session_duration?: number;
  messages_sent?: number;
  ai_interactions?: number;
}

// User interface events
export interface UIEvent extends BaseAnalyticsEvent {
  event_category: 'ui';
  page_path?: string;
  user_id?: string;
}

export interface NavigationEvent extends UIEvent {
  event_label: 'page_view' | 'navigation_click' | 'external_link_click';
  destination?: string;
  source_page?: string;
}

export interface ProfileEvent extends UIEvent {
  event_label: 'profile_viewed' | 'profile_edited' | 'settings_changed';
  section?: string;
  setting_changed?: string;
}

export interface FeatureUsageEvent extends UIEvent {
  event_label: 'feature_used' | 'feature_discovered' | 'feature_abandoned';
  feature_name: string;
  usage_context?: string;
}

// AI interaction events
export interface AIEvent extends BaseAnalyticsEvent {
  event_category: 'ai_interaction';
  user_id?: string;
  world_id?: string;
  character_id?: string;
}

export interface AIMessageEvent extends AIEvent {
  event_label: 'message_sent' | 'message_received' | 'message_failed';
  message_type?: 'quest' | 'character' | 'world' | 'general';
  response_time?: number;
  message_length?: number;
  tokens_used?: number;
}

export interface AIGenerationEvent extends AIEvent {
  event_label: 'content_generated' | 'generation_failed' | 'generation_retry';
  content_type: 'world' | 'character' | 'quest' | 'story';
  generation_time?: number;
  quality_rating?: number;
}

// Error and performance events
export interface ErrorEvent extends BaseAnalyticsEvent {
  event_category: 'error';
  error_type: string;
  error_message?: string;
  page_path?: string;
  user_id?: string;
}

export interface PerformanceEvent extends BaseAnalyticsEvent {
  event_category: 'performance';
  event_label: 'page_load' | 'api_response' | 'component_render';
  duration: number;
  page_path?: string;
}

// Union type for all possible analytics events
export type AnalyticsEvent = 
  | LoginEvent
  | SignupEvent
  | LogoutEvent
  | SubscriptionChangeEvent
  | UsageLimitEvent
  | UpgradePromptEvent
  | WorldCreationEvent
  | WorldInteractionEvent
  | CharacterCreationEvent
  | CharacterInteractionEvent
  | QuestProgressEvent
  | CampaignInteractionEvent
  | NavigationEvent
  | ProfileEvent
  | FeatureUsageEvent
  | AIMessageEvent
  | AIGenerationEvent
  | ErrorEvent
  | PerformanceEvent;

// Google Analytics gtag event parameters
export interface GtagEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
  user_id?: string;
  session_id?: string;
  page_title?: string;
  page_location?: string;
  [key: string]: any;
}

// Analytics configuration
export interface AnalyticsConfig {
  measurementId: string;
  enabled: boolean;
  debugMode: boolean;
  respectDoNotTrack: boolean;
  anonymizeIp: boolean;
  cookieConsent: boolean;
}

// User consent and privacy settings
export interface AnalyticsConsent {
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
  timestamp: Date;
  version: string;
}

// Analytics context for tracking user state
export interface AnalyticsContext {
  userId?: string;
  subscriptionTier?: SubscriptionTier;
  sessionId?: string;
  pageTitle?: string;
  pagePath?: string;
  userAgent?: string;
  referrer?: string;
}
