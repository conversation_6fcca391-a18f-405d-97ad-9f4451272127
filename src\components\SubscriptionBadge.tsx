import { SubscriptionTier } from '@/types/schema.types';
import { getTierConfig, getAllTiers, getMonthlyPrice } from '@/config/subscription.config';

interface SubscriptionBadgeProps {
  tier: SubscriptionTier;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export default function SubscriptionBadge({
  tier,
  size = 'md',
  showIcon = true,
  className = ''
}: SubscriptionBadgeProps) {
  const getBadgeConfig = (tier: SubscriptionTier) => {
    const config = getTierConfig(tier);

    const colorMap = {
      gray: {
        bgColor: 'bg-gray-500/20',
        textColor: 'text-gray-300',
        borderColor: 'border-gray-500/30',
      },
      primary: {
        bgColor: 'bg-primary/20',
        textColor: 'text-primary',
        borderColor: 'border-primary/30',
      },
      accent: {
        bgColor: config.badge.gradient
          ? 'bg-gradient-to-r from-accent/20 to-primary/20'
          : 'bg-accent/20',
        textColor: 'text-accent',
        borderColor: 'border-accent/30',
      },
    };

    const colors = colorMap[config.badge.color];

    return {
      label: config.displayName,
      bgColor: colors.bgColor,
      textColor: colors.textColor,
      borderColor: colors.borderColor,
      icon: config.badge.icon,
      hasRainbowShimmer: tier === 'pro_plus' && config.badge.gradient
    };
  };

  const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'md':
        return 'px-3 py-1 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1 text-sm';
    }
  };

  const config = getBadgeConfig(tier);
  const sizeClasses = getSizeClasses(size);

  return (
    <span
      className={`
        inline-flex items-center gap-1 rounded-full border font-medium relative overflow-hidden
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        ${sizeClasses}
        ${className}
      `}
    >
      {/* Rainbow shimmer animation for Pro+ tier */}
      {config.hasRainbowShimmer && (
        <>
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/30 via-yellow-500/30 via-green-500/30 via-blue-500/30 via-indigo-500/30 to-purple-500/30 animate-rainbow-shimmer"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer-sweep"></div>
        </>
      )}
      <span className="relative z-10 flex items-center gap-1">
        {showIcon && <span className="text-xs">{config.icon}</span>}
        {config.label}
      </span>
    </span>
  );
}

// Helper component for subscription tier comparison
interface SubscriptionComparisonProps {
  currentTier: SubscriptionTier;
  className?: string;
}

export function SubscriptionComparison({ currentTier, className = '' }: SubscriptionComparisonProps) {
  const tiers = getAllTiers().map(config => {
    const features: string[] = [];

    // Add limit-based features
    features.push(`${config.limits.worlds_per_week} worlds / week`);
    features.push(`${config.limits.messages_per_month} messages / month`);

    // Add features from the config (they're already strings)
    features.push(...config.features);

    return {
      tier: config.tier,
      name: config.displayName,
      price: getMonthlyPrice(config.tier) + '/month',
      features
    };
  });

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${className}`}>
      {tiers.map((tierInfo) => (
        <div
          key={tierInfo.tier}
          className={`
            p-4 rounded-lg border transition-all
            ${currentTier === tierInfo.tier
              ? 'border-primary bg-primary/10'
              : 'border-gray-800 bg-background-darker'
            }
          `}
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medieval text-lg">{tierInfo.name}</h3>
            <SubscriptionBadge tier={tierInfo.tier} size="sm" />
          </div>
          <div className="text-2xl font-bold text-primary mb-4">{tierInfo.price}</div>
          <ul className="space-y-2">
            {tierInfo.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-text-secondary">
                <span className="text-primary">✓</span>
                {feature}
              </li>
            ))}
          </ul>
          {currentTier === tierInfo.tier && (
            <div className="mt-4 text-center">
              <span className="text-primary text-sm font-medium">Current Plan</span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
