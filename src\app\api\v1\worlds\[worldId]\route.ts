import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireWorldOwnerOrAdmin } from '@/lib/auth-helpers';
import { trackWorld } from '@/lib/analytics';
import type { World } from '@/types/schema.types';

/**
 * GET /api/v1/worlds/[worldId]
 * Get world details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { worldId } = await params; // Destructure worldId directly

    // 1. Authorize: Ensure the user owns the world or is an admin
    // This helper likely returns basic world data or throws an error if unauthorized
    await requireWorldOwnerOrAdmin(session, worldId);

    // Track world view
    trackWorld('viewed', worldId, {
      user_id: session.user.id,
    });

    // 2. Fetch World Data with Characters after successful authorization
    const { data: worldWithCharacters, error: fetchError } = await supabaseAdmin
      .from('worlds')
      .select('*, characters(*)') // Fetch world columns and all columns from related characters
      .eq('id', worldId)
      .single();

    if (fetchError) {
      console.error(`Error fetching world ${worldId} with characters:`, fetchError);
      // Handle potential errors like world not found after auth (unlikely but possible)
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'World not found' }, { status: 404 });
      }
      return NextResponse.json({ error: `Failed to fetch world details: ${fetchError.message}` }, { status: 500 });
    }

    // 3. Return the enriched world data
    return NextResponse.json(worldWithCharacters);

  } catch (error: any) { // Catch potential errors from requireUserSession or requireWorldOwnerOrAdmin
    console.error('Error in GET /api/v1/worlds/[worldId]:', error);
    // Handle specific auth errors if they have identifiable properties, otherwise return generic error
    if (error.message?.includes('Unauthorized') || error.status === 401) {
       return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
     if (error.message?.includes('Forbidden') || error.status === 403) {
       return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/worlds/[worldId]
 * Update world details
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { worldId } = await params;
    const updates = await request.json();
    const world = await requireWorldOwnerOrAdmin(session, worldId);

    // Prevent updating user_id
    if (updates.user_id) {
      delete updates.user_id;
    }

    // Add updated_at timestamp
    updates.updated_at = new Date().toISOString();

    // Track world edit
    trackWorld('edited', worldId, {
      user_id: session.user.id,
      fields_updated: Object.keys(updates),
    });

    // Update the world
    const { data: updatedWorld, error: updateError } = await supabaseAdmin
      .from('worlds')
      .update(updates)
      .eq('id', worldId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json(updatedWorld as World);
  } catch (error) {
    console.error('Error updating world:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/worlds/[worldId]
 * Delete world
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { worldId } = await params;
    const world = await requireWorldOwnerOrAdmin(session, worldId);

    // Track world deletion
    trackWorld('deleted', worldId, {
      user_id: session.user.id,
      world_name: world.name,
    });

    // Delete the world
    const { error: deleteError } = await supabaseAdmin
      .from('worlds')
      .delete()
      .eq('id', worldId);

    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'World deleted successfully' });
  } catch (error) {
    console.error('Error deleting world:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
