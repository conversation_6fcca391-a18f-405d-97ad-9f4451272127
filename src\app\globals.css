@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=MedievalSharp&family=Roboto:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #8B5CF6;
  --primary-dark: #6D28D9;
  --secondary: #F59E0B;
  --secondary-dark: #D97706;
  --background: #111827;
  --background-darker: #0B1120;
  --accent: #EF4444;
  --accent-dark: #B91C1C;
  --text: #F9FAFB;
  --text-secondary: #D1D5DB;

  --font-body: 'Roboto', sans-serif;
  --font-fantasy: 'Cinzel', serif;
  --font-medieval: 'MedievalSharp', cursive;
}

body {
  @apply bg-background text-text;
  font-family: var(--font-body);
  background-image:
    linear-gradient(to bottom, rgba(11, 17, 32, 0.95), rgba(17, 24, 39, 0.98)),
    url('/images/fantasy-bg.jpg');
  background-attachment: fixed;
  background-size: cover;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-fantasy);
}

.medieval-text {
  font-family: var(--font-medieval);
}

/* Fantasy-themed components */
.scroll-parchment {
  @apply bg-amber-50 bg-opacity-10 border border-amber-900/30 rounded-md shadow-md p-6;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23d97706' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.fantasy-button {
  @apply bg-primary hover:bg-primary-dark text-white font-medieval py-2 px-6 rounded-lg shadow-lg transition duration-300 border border-primary-dark;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.fantasy-input {
  @apply bg-background-darker border border-gray-700 rounded-md px-4 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent outline-none;
}

.fantasy-card {
  @apply bg-background-darker border border-gray-800 rounded-lg overflow-hidden shadow-xl;
}

.fantasy-card-header {
  @apply bg-gradient-to-r from-primary to-primary-dark text-white p-4 font-fantasy;
}

/* Message styles for campaign messaging */
.message {
  @apply my-2 p-3 rounded-lg max-w-[80%];
}

.message-player {
  @apply bg-primary bg-opacity-20 border border-primary border-opacity-30 ml-auto;
}

.message-ai {
  @apply bg-secondary bg-opacity-20 border border-secondary border-opacity-30 mr-auto;
}

/* Loading animation for API calls */
.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots div {
  @apply w-2 h-2 bg-white rounded-full animate-pulse;
}

.loading-dots div:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots div:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes fade-slide-in {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-slide-in {
  animation: fade-slide-in 0.3s ease-out forwards;
}

/* Rainbow shimmer animations for Pro+ subscription badge */
@keyframes rainbow-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes shimmer-sweep {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-rainbow-shimmer {
  background-size: 200% 100%;
  animation: rainbow-shimmer 3s ease-in-out infinite;
}

.animate-shimmer-sweep {
  animation: shimmer-sweep 2s ease-in-out infinite;
}

/* Hide number input spinners */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

/* Hide caret and disable text selection on non-editable elements */
body *:not(input):not(textarea):not([contenteditable="true"]) {
  caret-color: transparent;
}

/* Explicitly restore caret and selection for form fields */
input,
textarea,
[contenteditable="true"] {
  caret-color: auto;
  user-select: auto;
}

/* Base typography */
p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

a {
  color: var(--primary);
  text-decoration: none; /* Removed underline */
}

a:hover {
  color: var(--primary-dark);
}

button,
input[type="button"],
input[type="submit"] {
  cursor: pointer;
}

/* Focus styles for accessibility */
/* a:focus,
button:focus,
input:focus {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.4);
} */
