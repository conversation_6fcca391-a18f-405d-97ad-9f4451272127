/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary)',
        'primary-dark': 'var(--primary-dark)',
        secondary: 'var(--secondary)',
        'secondary-dark': 'var(--secondary-dark)',
        background: 'var(--background)',
        'background-darker': 'var(--background-darker)',
        accent: 'var(--accent)',
        'accent-dark': 'var(--accent-dark)',
        text: 'var(--text)',
        'text-secondary': 'var(--text-secondary)',
      },
      fontFamily: {
        body: ['var(--font-body)'],
        fantasy: ['var(--font-fantasy)'],
        medieval: ['var(--font-medieval)'],
      },
      animation: {
        'rainbow-shimmer': 'rainbow-shimmer 3s ease-in-out infinite',
        'shimmer-sweep': 'shimmer-sweep 2s ease-in-out infinite',
      },
      keyframes: {
        'rainbow-shimmer': {
          '0%': { 'background-position': '-200% 0' },
          '100%': { 'background-position': '200% 0' },
        },
        'shimmer-sweep': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
    },
  },
  plugins: [],
};