'use client'

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import Script from 'next/script';
import { env } from '@/lib/env';
import { 
  initializeAnalytics, 
  setAnalyticsContext, 
  trackPageView,
  trackAuth 
} from '@/lib/analytics';
import { AnalyticsConsent } from '@/types/analytics.types';

interface GoogleAnalyticsProps {
  measurementId?: string;
}

export default function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const gaId = measurementId || env.GA_MEASUREMENT_ID;

  // Initialize analytics when component mounts
  useEffect(() => {
    if (!gaId) return;

    // Default consent settings (can be overridden by user preferences)
    const defaultConsent: AnalyticsConsent = {
      analytics: true, // Enable by default, but respect user preferences
      marketing: false, // Disable marketing by default
      functional: true, // Enable functional cookies
      timestamp: new Date(),
      version: '1.0',
    };

    // Check for stored user preferences
    const storedConsent = localStorage.getItem('analytics_consent');
    const userConsent = storedConsent ? JSON.parse(storedConsent) : defaultConsent;

    initializeAnalytics(userConsent);
  }, [gaId]);

  // Update analytics context when session changes
  useEffect(() => {
    if (status === 'loading') return;

    setAnalyticsContext({
      userId: session?.user?.id,
      subscriptionTier: session?.user?.subscription_tier,
      sessionId: session?.sessionToken || undefined,
      pagePath: pathname,
      pageTitle: document.title,
    });

    // Track authentication events
    if (status === 'authenticated' && session?.user) {
      // Check if this is a new session (simple check)
      const lastAuthTime = sessionStorage.getItem('last_auth_time');
      const currentTime = Date.now().toString();
      
      if (!lastAuthTime || (Date.now() - parseInt(lastAuthTime)) > 30 * 60 * 1000) {
        // Track login if no recent auth or more than 30 minutes ago
        trackAuth('login', {
          auth_method: 'google',
          user_id: session.user.id,
          subscription_tier: session.user.subscription_tier,
        });
        sessionStorage.setItem('last_auth_time', currentTime);
      }
    }
  }, [session, status, pathname]);

  // Track page views when pathname changes
  useEffect(() => {
    if (pathname) {
      trackPageView(pathname);
    }
  }, [pathname]);

  // Don't render scripts if no measurement ID
  if (!gaId) {
    return null;
  }

  return (
    <>
      {/* Google Analytics gtag script */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
        strategy="afterInteractive"
        onLoad={() => {
          console.log('Google Analytics script loaded');
        }}
      />
      
      {/* Initialize gtag */}
      <Script
        id="google-analytics-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
          `,
        }}
      />
    </>
  );
}

// Hook for managing analytics consent
export function useAnalyticsConsent() {
  const updateConsent = (consent: Partial<AnalyticsConsent>) => {
    const currentConsent = localStorage.getItem('analytics_consent');
    const existingConsent = currentConsent ? JSON.parse(currentConsent) : {};
    
    const newConsent: AnalyticsConsent = {
      ...existingConsent,
      ...consent,
      timestamp: new Date(),
      version: '1.0',
    };

    localStorage.setItem('analytics_consent', JSON.stringify(newConsent));
    
    // Update analytics with new consent
    import('@/lib/analytics').then(({ updateConsent: updateAnalyticsConsent }) => {
      updateAnalyticsConsent(newConsent);
    });

    return newConsent;
  };

  const getConsent = (): AnalyticsConsent | null => {
    const stored = localStorage.getItem('analytics_consent');
    return stored ? JSON.parse(stored) : null;
  };

  const revokeConsent = () => {
    const revokedConsent: AnalyticsConsent = {
      analytics: false,
      marketing: false,
      functional: false,
      timestamp: new Date(),
      version: '1.0',
    };

    localStorage.setItem('analytics_consent', JSON.stringify(revokedConsent));
    
    // Disable analytics
    import('@/lib/analytics').then(({ disableAnalytics }) => {
      disableAnalytics();
    });

    return revokedConsent;
  };

  return {
    updateConsent,
    getConsent,
    revokeConsent,
  };
}
