{"name": "dnd-ai-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@supabase/supabase-js": "^2.49.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.15", "bufferutil": "^4.0.9", "clsx": "^2.1.1", "framer-motion": "^12.6.2", "gtag": "^1.0.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "next": "^15.2.4", "next-auth": "^4.23.1", "postcss": "^8.4.28", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "swr": "^2.3.3", "tailwindcss": "^3.3.3", "typescript": "^5.1.6", "utf-8-validate": "^6.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@types/jest": "^30.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.30.1", "eslint-config-next": "^15.2.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.2.0", "typescript-eslint": "^8.38.0"}}