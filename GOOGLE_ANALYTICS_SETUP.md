# Google Analytics 4 Integration Guide for SoloQuest

## Overview

This guide provides comprehensive instructions for integrating Google Analytics 4 (GA4) into the SoloQuest web application. The implementation includes page view tracking, custom event tracking for user interactions, subscription system monitoring, and GDPR-compliant privacy controls.

## Prerequisites

- Google account with access to Google Analytics
- Admin access to the SoloQuest application
- Node.js and npm/yarn installed
- Basic understanding of Next.js and TypeScript

## Part 1: Google Analytics 4 Setup

### Step 1: Create a Google Analytics 4 Property

1. **Access Google Analytics**
   - Go to [Google Analytics](https://analytics.google.com/)
   - Sign in with your Google account

2. **Create a New Property**
   - Click "Admin" (gear icon) in the bottom left
   - In the "Property" column, click "Create Property"
   - Choose "GA4" (Google Analytics 4)

3. **Configure Property Settings**
   - **Property name**: `SoloQuest Web Application`
   - **Reporting time zone**: Select your preferred timezone
   - **Currency**: Select your preferred currency (USD recommended)
   - Click "Next"

4. **Business Information**
   - **Industry category**: `Games` or `Entertainment`
   - **Business size**: Select appropriate size
   - **Intended use**: Select relevant options (e.g., "Measure customer engagement")
   - Click "Create"

5. **Accept Terms of Service**
   - Review and accept the Google Analytics Terms of Service
   - Accept Data Processing Terms if prompted

### Step 2: Set Up Data Stream

1. **Create Web Data Stream**
   - After property creation, you'll be prompted to set up a data stream
   - Click "Web" platform
   - **Website URL**:
     - Production: `https://your-domain.com`
     - Development: `http://localhost:3000`
   - **Stream name**: `SoloQuest Web Stream`
   - Click "Create stream"

2. **Get Measurement ID**
   - After creating the stream, you'll see the **Measurement ID** (format: `G-XXXXXXXXXX`)
   - **IMPORTANT**: Copy this ID - you'll need it for environment configuration
   - The Measurement ID will be displayed at the top of the stream details page

### Step 3: Configure Enhanced Measurement (Recommended)

1. **Access Enhanced Measurement**
   - In your data stream settings, scroll to "Enhanced measurement"
   - Click the gear icon next to "Enhanced measurement"

2. **Enable Recommended Events**
   - ✅ **Page views** (automatically enabled)
   - ✅ **Scrolls** (track user engagement)
   - ✅ **Outbound clicks** (track external link clicks)
   - ✅ **Site search** (if you add search functionality)
   - ✅ **File downloads** (track PDF/document downloads)
   - ❌ **Video engagement** (disable if not using videos)

## Part 2: Environment Configuration

### Step 1: Update Environment Variables

1. **Add to `.env.local`** (create if it doesn't exist):
```bash
# Google Analytics 4 Configuration
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

2. **Add to `.env.example`**:
```bash
# Google Analytics 4 Configuration
NEXT_PUBLIC_GA_MEASUREMENT_ID=your-ga4-measurement-id
```

### Step 2: Update Environment Configuration

The application will automatically include the new environment variable in the existing `src/lib/env.ts` configuration system.

## Part 3: Privacy and GDPR Compliance

### Privacy Considerations

1. **Data Collection Transparency**
   - Inform users about data collection in your privacy policy
   - Provide clear opt-out mechanisms
   - Respect user privacy preferences

2. **GDPR Compliance**
   - Implement consent management for EU users
   - Provide data deletion capabilities
   - Honor "Do Not Track" browser settings

3. **Data Retention**
   - Configure appropriate data retention periods in GA4
   - Regularly review and clean up old data
   - Document data processing activities

### Recommended Privacy Policy Updates

Add the following section to your privacy policy:

```markdown
## Analytics and Tracking

We use Google Analytics 4 to understand how users interact with our application. This helps us improve the user experience and identify areas for enhancement.

**Data Collected:**
- Page views and navigation patterns
- User interactions (button clicks, form submissions)
- Subscription tier changes and usage patterns
- Quest completion rates and engagement metrics
- Technical information (browser type, device type, screen resolution)

**Data Usage:**
- Improve application performance and user experience
- Understand feature usage and user preferences
- Identify and fix technical issues
- Measure the effectiveness of new features

**Your Privacy Rights:**
- You can opt out of analytics tracking in your profile settings
- We respect "Do Not Track" browser settings
- EU users have additional rights under GDPR
- Contact us to request data deletion or access

**Data Sharing:**
- Analytics data is processed by Google Analytics
- No personally identifiable information is shared with third parties
- Data is aggregated and anonymized where possible
```

## Part 4: Testing and Validation

### Development Testing

1. **Local Environment Setup**
   ```bash
   # Set your GA4 Measurement ID in .env.local
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

   # Start development server
   yarn dev
   ```

2. **Browser Developer Tools Testing**
   - Open Chrome/Firefox Developer Tools (F12)
   - Go to Network tab and filter by "google-analytics" or "gtag"
   - Navigate through your application
   - Verify analytics requests are being sent
   - Check Console tab for any analytics-related errors

3. **Google Analytics Debugger Extension**
   - Install [Google Analytics Debugger](https://chrome.google.com/webstore/detail/google-analytics-debugger/jnkmfdileelhofjcijamephohjechhna) for Chrome
   - Enable the extension
   - Navigate through your application
   - Check browser console for detailed GA event logs

4. **Real-time Reports Verification**
   - Go to [Google Analytics](https://analytics.google.com/)
   - Navigate to Reports → Realtime → Overview
   - Use your local application (localhost:3000)
   - Verify page views appear in real-time
   - Test key user actions (signup, world creation, profile visits)

### Production Validation

1. **GA4 Debug View Setup**
   ```javascript
   // Add this to your .env.local for production debugging
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
   ```
   - In GA4, go to Configure → DebugView
   - Enable debug mode for your production domain
   - Test all major user flows in production

2. **Event Verification Checklist**
   - ✅ **Page Views**: Navigate between pages
   - ✅ **Authentication**: Sign up, log in, log out
   - ✅ **World Management**: Create, view, edit, delete worlds
   - ✅ **Character Management**: Create, view, edit characters
   - ✅ **AI Interactions**: Send messages, receive responses
   - ✅ **Subscription Events**: Tier changes, limit warnings
   - ✅ **Profile Actions**: Tab changes, settings updates

3. **Custom Event Testing**
   ```javascript
   // Test custom events in browser console
   gtag('event', 'test_event', {
     event_category: 'test',
     event_label: 'manual_test',
     custom_parameters: {
       test_parameter: 'test_value'
     }
   });
   ```

### Analytics Dashboard Setup

1. **Create Custom Dashboard**
   - Go to GA4 → Reports → Library
   - Click "Create new report" → "Create new collection"
   - Add the following reports:

2. **Key Reports to Create**
   - **User Engagement Report**
     - Page views by page path
     - Session duration
     - User retention

   - **Subscription Analytics**
     - Subscription tier distribution
     - Upgrade/downgrade events
     - Usage limit events

   - **Feature Usage Report**
     - World creation events
     - Character creation events
     - AI interaction events

3. **Set Up Conversion Goals**
   - Go to Configure → Events
   - Mark key events as conversions:
     - `signup` (user registration)
     - `subscription_tier_change` (subscription upgrades)
     - `world_created` (content creation)
     - `ai_message_sent` (feature engagement)

### Key Metrics to Monitor

1. **User Engagement Metrics**
   - **Page Views**: Track most visited pages
   - **Session Duration**: Average time spent in app
   - **User Retention**: Return visit patterns
   - **Feature Adoption**: Which features are used most

2. **Subscription System Metrics**
   - **Tier Distribution**: Free vs Pro vs Pro+ users
   - **Upgrade Events**: Conversion from free to paid
   - **Usage Patterns**: Message/world creation by tier
   - **Limit Encounters**: How often users hit limits

3. **Content Creation Metrics**
   - **World Creation Rate**: Worlds created per user
   - **Character Creation**: Characters per world
   - **AI Interaction**: Messages sent per session
   - **Quest Completion**: Campaign engagement

4. **Technical Performance**
   - **Page Load Times**: Performance tracking
   - **API Response Times**: AI interaction speed
   - **Error Rates**: Failed requests and errors

### Troubleshooting Common Issues

1. **Events Not Appearing in GA4**
   ```bash
   # Check if Measurement ID is set correctly
   echo $NEXT_PUBLIC_GA_MEASUREMENT_ID

   # Verify in browser console
   console.log(window.gtag);
   ```
   - Verify Measurement ID format (G-XXXXXXXXXX)
   - Check browser console for JavaScript errors
   - Ensure ad blockers aren't interfering
   - Confirm user has accepted analytics consent

2. **Development vs Production Issues**
   - Use separate GA4 properties for dev/staging/prod
   - Set up hostname filters to separate environments
   - Check environment variable loading

3. **Privacy and Consent Issues**
   - Verify consent banner is showing for new users
   - Test opt-out functionality
   - Check that events stop firing when consent is revoked
   - Ensure GDPR compliance for EU users

4. **Performance Impact**
   - Monitor bundle size impact
   - Check for analytics script loading delays
   - Verify async loading of GA scripts

### Validation Checklist

Before going live, ensure:

- [ ] GA4 property is properly configured
- [ ] Measurement ID is correctly set in environment variables
- [ ] All key events are firing in development
- [ ] Real-time reports show data in GA4
- [ ] Consent banner appears for new users
- [ ] Privacy settings work correctly
- [ ] Custom events include proper parameters
- [ ] Error tracking is working
- [ ] Performance impact is minimal
- [ ] GDPR compliance is implemented

## Part 5: Advanced Configuration

### Custom Dimensions (Optional)

Set up custom dimensions in GA4 for enhanced tracking:

1. **User Properties**
   - `subscription_tier`: Track user subscription level
   - `user_type`: New vs returning users
   - `registration_date`: User cohort analysis

2. **Event Parameters**
   - `world_count`: Number of worlds created
   - `character_count`: Number of characters created
   - `message_usage`: Current message usage

### Conversion Goals

Set up conversion goals for key business metrics:

1. **User Registration**: Track new user signups
2. **Subscription Upgrades**: Track tier changes
3. **Quest Completions**: Track engagement milestones
4. **World Creation**: Track content creation

## Troubleshooting

### Common Issues

1. **Events Not Appearing**
   - Check Measurement ID is correct
   - Verify environment variables are loaded
   - Check browser console for errors
   - Ensure ad blockers aren't interfering

2. **Development vs Production**
   - Use separate GA4 properties for dev/prod
   - Filter out internal traffic in production
   - Set up proper hostname filtering

3. **Privacy Compliance**
   - Implement proper consent management
   - Test opt-out functionality
   - Verify data anonymization

### Support Resources

- [Google Analytics 4 Documentation](https://developers.google.com/analytics/devguides/collection/ga4)
- [Next.js Analytics Integration](https://nextjs.org/docs/basic-features/built-in-css-support)
- [GDPR Compliance Guide](https://support.google.com/analytics/answer/9019185)

## Next Steps

After completing this setup:

1. Monitor analytics data for the first week
2. Set up custom dashboards for key metrics
3. Configure automated reports for stakeholders
4. Regularly review and optimize tracking implementation
5. Stay updated with GA4 feature releases and best practices

---

**Note**: This integration respects user privacy and follows best practices for web analytics. Always ensure compliance with applicable privacy laws and regulations in your jurisdiction.
