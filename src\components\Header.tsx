'use client'

import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { getLastPlayedWorld } from '@/utils/world-utils'
import SubscriptionBadge from '@/components/SubscriptionBadge'

export default function Header() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { data: session, status } = useSession()
  const [lastPlayedWorldId, setLastPlayedWorldId] = useState<string | null>(null)

  const isLoading = status === 'loading'
  const isAuthenticated = status === 'authenticated'

  // Get last played world ID from localStorage
  useEffect(() => {
    const lastWorldId = getLastPlayedWorld()
    setLastPlayedWorldId(lastWorldId)
  }, [])

  const isActive = (path: string) => {
    // Special case for the Play link
    if (path.startsWith('/world/') && pathname.startsWith('/world/')) {
      return true
    }
    return pathname === path
  }

  const links = [
    { href: '/', label: 'Home' },
    { href: '/worlds', label: 'Worlds' },
  ]

  // Add Play link if there's a last played world
  if (lastPlayedWorldId) {
    links.push({ href: `/world/${lastPlayedWorldId}`, label: 'Play' })
  }

  return (
    <header className="bg-background-darker/95 backdrop-blur-sm border-b border-gray-800/50 py-3 sticky top-0 z-50 shadow-lg shadow-black/20">
      <div className="container mx-auto px-4 lg:px-6">
        <div className="flex justify-between items-center h-14">
          {/* Logo Section - Enhanced with better spacing and hover effects */}
          <Link
            href="/"
            className="flex items-center space-x-3 group transition-all duration-300 hover:scale-105"
            aria-label="SoloQuest Home"
          >
            <div className="w-10 h-10 relative transition-transform duration-300 group-hover:rotate-12">
              <Image
                src="/images/d20-icon.svg"
                alt="SoloQuest Logo"
                width={40}
                height={40}
                className="object-contain drop-shadow-lg"
              />
            </div>
            <span className="text-xl font-medieval text-primary group-hover:text-primary-dark transition-colors duration-300 tracking-wide">
              SoloQuest
            </span>
          </Link>

          {/* Mobile menu button - Enhanced with better animations */}
          <button
            className="md:hidden p-2 rounded-lg text-white hover:bg-primary/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            aria-expanded={isMenuOpen}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={2}
              stroke="currentColor"
              className={`w-6 h-6 transition-transform duration-300 ${isMenuOpen ? 'rotate-90' : ''}`}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5"}
              />
            </svg>
          </button>

          {/* Desktop Navigation - Enhanced spacing and alignment */}
          <nav className="hidden md:flex items-center space-x-8">
            {/* Navigation Links */}
            <div className="flex items-center space-x-6">
              {links.map((link) => (
                <NavLink
                  key={link.href}
                  href={link.href}
                  text={link.label}
                  isActive={isActive(link.href)}
                />
              ))}
            </div>

            {/* Authentication Section - Enhanced with better visual hierarchy */}
            <div className="flex items-center space-x-4 pl-6 border-l border-gray-700/50">
              {isLoading ? (
                <div className="w-28 h-10 bg-background-darker/50 animate-pulse rounded-lg border border-gray-800/30"></div>
              ) : isAuthenticated ? (
                <div className="flex items-center space-x-3">
                  {/* Subscription Badge for authenticated users */}
                  {session?.user?.subscription_tier && (
                    <SubscriptionBadge
                      tier={session.user.subscription_tier}
                      size="sm"
                      className="hidden lg:inline-flex"
                    />
                  )}
                  <ProfileLink />
                </div>
              ) : (
                <Link
                  href="/signup"
                  className="fantasy-button text-sm px-6 py-2.5 font-medium tracking-wide hover:shadow-lg hover:shadow-primary/25 transition-all duration-300"
                >
                  Sign Up
                </Link>
              )}
            </div>
          </nav>
        </div>
      </div>

      {/* Mobile Navigation - Enhanced with better animations and spacing */}
      <div className={`md:hidden transition-all duration-300 ease-in-out ${
        isMenuOpen
          ? 'max-h-96 opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <nav className="bg-background-darker/98 backdrop-blur-sm border-t border-gray-800/50 mt-3 py-4 shadow-lg shadow-black/10">
          <div className="container mx-auto px-4">
            <div className="flex flex-col space-y-1">
              {/* Navigation Links */}
              {links.map((link, index) => (
                <div
                  key={link.href}
                  className="animate-fade-slide-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <MobileNavLink
                    href={link.href}
                    text={link.label}
                    isActive={isActive(link.href)}
                    onClick={() => setIsMenuOpen(false)}
                  />
                </div>
              ))}

              {/* Divider */}
              <div className="border-t border-gray-700/50 my-3"></div>

              {/* Authentication Section */}
              {isLoading ? (
                <div className="w-full h-12 bg-background-darker/50 animate-pulse rounded-lg border border-gray-800/30 mx-2"></div>
              ) : isAuthenticated ? (
                <div className="space-y-1">
                  {/* Subscription Badge for mobile */}
                  {session?.user?.subscription_tier && (
                    <div className="px-4 py-2">
                      <SubscriptionBadge
                        tier={session.user.subscription_tier}
                        size="sm"
                      />
                    </div>
                  )}

                  <MobileNavLink
                    href="/profile"
                    text="Profile"
                    isActive={pathname === '/profile'}
                    onClick={() => setIsMenuOpen(false)}
                  />
                  <MobileNavLink
                    href="/worlds"
                    text="My Worlds"
                    isActive={pathname === '/worlds'}
                    onClick={() => setIsMenuOpen(false)}
                  />
                  <button
                    onClick={() => {
                      setIsMenuOpen(false)
                      signOut({ callbackUrl: '/' })
                    }}
                    className="w-full text-left px-4 py-3 rounded-lg text-sm font-medium text-accent hover:bg-accent/10 hover:text-accent-dark transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent/50"
                  >
                    Sign Out
                  </button>
                </div>
              ) : (
                <div className="px-2">
                  <Link
                    href="/signup"
                    onClick={() => setIsMenuOpen(false)}
                    className="fantasy-button text-sm w-full text-center py-3 font-medium tracking-wide"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        </nav>
      </div>
    </header>
  )
}

// Enhanced NavLink component with better hover effects and accessibility
function NavLink({ href, text, isActive }: { href: string; text: string; isActive: boolean }) {
  return (
    <Link
      href={href}
      className={`
        relative px-3 py-2 text-sm font-medium tracking-wide transition-all duration-300 rounded-lg
        focus:outline-none focus:ring-2 focus:ring-primary/50
        ${isActive
          ? 'text-primary bg-primary/10 shadow-sm'
          : 'text-text-secondary hover:text-primary hover:bg-primary/5'
        }
        before:absolute before:bottom-0 before:left-1/2 before:w-0 before:h-0.5 before:bg-primary
        before:transition-all before:duration-300 before:-translate-x-1/2
        ${isActive ? 'before:w-6' : 'hover:before:w-4'}
      `}
    >
      {text}
    </Link>
  )
}

// ProfileLink component for desktop navigation with dropdown
function ProfileLink() {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleNavigation = (url: string) => {
    setIsDropdownOpen(false)
    router.push(url)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="
          relative px-4 py-2.5 text-sm font-medium text-text-secondary
          hover:text-primary transition-all duration-300 rounded-lg
          hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/50
          border border-transparent hover:border-primary/20
          group flex items-center space-x-2
        "
        aria-expanded={isDropdownOpen}
        aria-haspopup="true"
      >
        <svg
          className="w-4 h-4 transition-transform duration-300 group-hover:scale-110"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
        <span>Profile</span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Dropdown Menu */}
      <div className={`
        absolute right-0 mt-2 w-48 bg-background-darker border border-gray-700/50 rounded-lg shadow-lg shadow-black/20 z-50 overflow-hidden
        transition-all duration-200 ease-out origin-top-right
        ${isDropdownOpen
          ? 'opacity-100 scale-100 translate-y-0'
          : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
        }
      `}>
        <div className="py-1">
          <button
            onClick={() => handleNavigation('/profile')}
            className="w-full text-left px-4 py-2 text-sm text-text-secondary hover:text-primary hover:bg-primary/5 transition-colors duration-200"
          >
            Profile
          </button>
          <button
            onClick={() => handleNavigation('/profile?tab=characters')}
            className="w-full text-left px-4 py-2 text-sm text-text-secondary hover:text-primary hover:bg-primary/5 transition-colors duration-200"
          >
            Characters
          </button>
          <button
            onClick={() => handleNavigation('/profile?tab=subscription')}
            className="w-full text-left px-4 py-2 text-sm text-text-secondary hover:text-primary hover:bg-primary/5 transition-colors duration-200"
          >
            Subscription
          </button>
          <button
            onClick={() => handleNavigation('/profile?tab=settings')}
            className="w-full text-left px-4 py-2 text-sm text-text-secondary hover:text-primary hover:bg-primary/5 transition-colors duration-200"
          >
            Settings
          </button>
          <div className="border-t border-gray-700/50 my-1"></div>
          <button
            onClick={() => {
              setIsDropdownOpen(false)
              signOut({ callbackUrl: '/' })
            }}
            className="w-full text-left px-4 py-2 text-sm text-accent hover:text-accent-dark hover:bg-accent/10 transition-colors duration-200"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  )
}

// MobileNavLink component for mobile navigation
function MobileNavLink({
  href,
  text,
  isActive,
  onClick
}: {
  href: string;
  text: string;
  isActive: boolean;
  onClick?: () => void;
}) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={`
        block px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-primary/50
        ${isActive
          ? 'text-primary bg-primary/15 border-l-4 border-primary shadow-sm'
          : 'text-text-secondary hover:text-primary hover:bg-primary/5 hover:translate-x-1'
        }
      `}
    >
      {text}
    </Link>
  )
}
