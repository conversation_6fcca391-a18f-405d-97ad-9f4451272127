import { env } from './env';
import { 
  AnalyticsEvent, 
  AnalyticsConfig, 
  AnalyticsContext, 
  AnalyticsConsent,
  GtagEventParams 
} from '@/types/analytics.types';

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'consent',
      targetId: string | 'default',
      config?: any
    ) => void;
    dataLayer: any[];
  }
}

// Analytics configuration
const analyticsConfig: AnalyticsConfig = {
  measurementId: env.GA_MEASUREMENT_ID,
  enabled: !!env.GA_MEASUREMENT_ID && env.isProduction(),
  debugMode: env.isDevelopment(),
  respectDoNotTrack: true,
  anonymizeIp: true,
  cookieConsent: false, // Will be set based on user consent
};

// Current analytics context
let analyticsContext: AnalyticsContext = {};

/**
 * Initialize Google Analytics
 */
export function initializeAnalytics(consent?: AnalyticsConsent): void {
  if (!analyticsConfig.measurementId || typeof window === 'undefined') {
    console.warn('Google Analytics: Measurement ID not found or running on server');
    return;
  }

  // Check Do Not Track setting
  if (analyticsConfig.respectDoNotTrack && navigator.doNotTrack === '1') {
    console.log('Google Analytics: Respecting Do Not Track setting');
    return;
  }

  // Initialize dataLayer
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag() {
    window.dataLayer.push(arguments);
  };

  // Set consent mode
  if (consent) {
    window.gtag('consent', 'default', {
      analytics_storage: consent.analytics ? 'granted' : 'denied',
      ad_storage: consent.marketing ? 'granted' : 'denied',
      functionality_storage: consent.functional ? 'granted' : 'denied',
    });
  }

  // Configure Google Analytics
  window.gtag('config', analyticsConfig.measurementId, {
    debug_mode: analyticsConfig.debugMode,
    anonymize_ip: analyticsConfig.anonymizeIp,
    allow_google_signals: consent?.marketing ?? false,
    allow_ad_personalization_signals: consent?.marketing ?? false,
  });

  analyticsConfig.enabled = true;
  analyticsConfig.cookieConsent = consent?.analytics ?? false;

  if (analyticsConfig.debugMode) {
    console.log('Google Analytics initialized:', {
      measurementId: analyticsConfig.measurementId,
      consent,
      config: analyticsConfig,
    });
  }
}

/**
 * Update user consent settings
 */
export function updateConsent(consent: AnalyticsConsent): void {
  if (typeof window === 'undefined' || !window.gtag) {
    return;
  }

  window.gtag('consent', 'update', {
    analytics_storage: consent.analytics ? 'granted' : 'denied',
    ad_storage: consent.marketing ? 'granted' : 'denied',
    functionality_storage: consent.functional ? 'granted' : 'denied',
  });

  analyticsConfig.cookieConsent = consent.analytics;
  analyticsConfig.enabled = consent.analytics && !!analyticsConfig.measurementId;

  if (analyticsConfig.debugMode) {
    console.log('Google Analytics consent updated:', consent);
  }
}

/**
 * Set analytics context for tracking user state
 */
export function setAnalyticsContext(context: Partial<AnalyticsContext>): void {
  analyticsContext = { ...analyticsContext, ...context };

  if (analyticsConfig.debugMode) {
    console.log('Analytics context updated:', analyticsContext);
  }
}

/**
 * Track page view
 */
export function trackPageView(path: string, title?: string): void {
  if (!analyticsConfig.enabled || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  const pageViewData = {
    page_title: title || document.title,
    page_location: window.location.href,
    page_path: path,
    user_id: analyticsContext.userId,
    custom_parameters: {
      subscription_tier: analyticsContext.subscriptionTier,
      session_id: analyticsContext.sessionId,
    },
  };

  window.gtag('event', 'page_view', pageViewData);

  if (analyticsConfig.debugMode) {
    console.log('Page view tracked:', pageViewData);
  }
}

/**
 * Track custom event
 */
export function trackEvent(eventName: string, event: AnalyticsEvent): void {
  if (!analyticsConfig.enabled || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  const eventData: GtagEventParams = {
    event_category: event.event_category,
    event_label: event.event_label,
    value: event.value,
    user_id: analyticsContext.userId,
    ...event.custom_parameters,
    // Add context data
    subscription_tier: analyticsContext.subscriptionTier,
    session_id: analyticsContext.sessionId,
    page_path: analyticsContext.pagePath,
  };

  window.gtag('event', eventName, eventData);

  if (analyticsConfig.debugMode) {
    console.log('Event tracked:', eventName, eventData);
  }
}

/**
 * Track user authentication events
 */
export function trackAuth(event: 'login' | 'signup' | 'logout', data?: any): void {
  trackEvent(event, {
    event_category: 'auth',
    event_label: event,
    custom_parameters: data,
  });
}

/**
 * Track subscription events
 */
export function trackSubscription(
  event: 'tier_change' | 'limit_reached' | 'upgrade_prompt',
  data: any
): void {
  trackEvent(`subscription_${event}`, {
    event_category: 'subscription',
    event_label: event,
    custom_parameters: data,
  });
}

/**
 * Track world and quest events
 */
export function trackWorld(
  event: 'created' | 'viewed' | 'edited' | 'deleted',
  worldId: string,
  data?: any
): void {
  trackEvent(`world_${event}`, {
    event_category: 'world',
    event_label: event,
    custom_parameters: {
      world_id: worldId,
      ...data,
    },
  });
}

/**
 * Track character events
 */
export function trackCharacter(
  event: 'created' | 'viewed' | 'edited' | 'deleted',
  characterId: string,
  data?: any
): void {
  trackEvent(`character_${event}`, {
    event_category: 'character',
    event_label: event,
    custom_parameters: {
      character_id: characterId,
      ...data,
    },
  });
}

/**
 * Track AI interaction events
 */
export function trackAI(
  event: 'message_sent' | 'content_generated' | 'error',
  data?: any
): void {
  trackEvent(`ai_${event}`, {
    event_category: 'ai_interaction',
    event_label: event,
    custom_parameters: data,
  });
}

/**
 * Track UI interaction events
 */
export function trackUI(
  event: 'feature_used' | 'navigation' | 'profile_action',
  data?: any
): void {
  trackEvent(`ui_${event}`, {
    event_category: 'ui',
    event_label: event,
    custom_parameters: data,
  });
}

/**
 * Track errors
 */
export function trackError(error: Error, context?: any): void {
  trackEvent('error', {
    event_category: 'error',
    event_label: error.name,
    custom_parameters: {
      error_message: error.message,
      error_stack: error.stack,
      page_path: window.location.pathname,
      ...context,
    },
  });
}

/**
 * Track performance metrics
 */
export function trackPerformance(
  metric: 'page_load' | 'api_response' | 'component_render',
  duration: number,
  data?: any
): void {
  trackEvent(`performance_${metric}`, {
    event_category: 'performance',
    event_label: metric,
    value: Math.round(duration),
    custom_parameters: data,
  });
}

/**
 * Get current analytics configuration
 */
export function getAnalyticsConfig(): AnalyticsConfig {
  return { ...analyticsConfig };
}

/**
 * Check if analytics is enabled and user has consented
 */
export function isAnalyticsEnabled(): boolean {
  return analyticsConfig.enabled && analyticsConfig.cookieConsent;
}

/**
 * Disable analytics tracking
 */
export function disableAnalytics(): void {
  analyticsConfig.enabled = false;
  analyticsConfig.cookieConsent = false;
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('consent', 'update', {
      analytics_storage: 'denied',
      ad_storage: 'denied',
    });
  }
}

/**
 * Enable analytics tracking (requires user consent)
 */
export function enableAnalytics(): void {
  if (analyticsConfig.measurementId) {
    analyticsConfig.enabled = true;
    analyticsConfig.cookieConsent = true;
    
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'granted',
      });
    }
  }
}
