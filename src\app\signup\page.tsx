'use client'

import { useState, useEffect } from 'react'
import { signIn, useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { trackAuth } from '@/lib/analytics'

export default function SignupPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Redirect to profile if already authenticated
  useEffect(() => {
    if (status === 'authenticated') {
      router.push('/profile')
    }
  }, [status, router])

  const handleGoogleSignIn = async () => {
    setIsLoading(true)

    // Track signup attempt
    trackAuth('signup', {
      auth_method: 'google',
      signup_source: 'signup_page',
    })

    await signIn('google', { callbackUrl: '/profile' })
  }

  return (
    <main className="container mx-auto py-12 px-4 min-h-[calc(100vh-200px)] flex items-center justify-center">
      <div className="max-w-md w-full bg-background-darker border border-gray-800 rounded-lg overflow-hidden shadow-xl">
        <div className="bg-gradient-to-r from-background-darker to-primary/20 p-6 text-center">
          <div className="w-16 h-16 mx-auto relative mb-4">
            <Image
              src="/images/d20-icon.svg"
              alt="SoloQuest Logo"
              width={64}
              height={64}
              className="object-contain"
            />
          </div>
          <h1 className="text-2xl font-medieval text-primary">Join the Adventure</h1>
          <p className="text-text-secondary mt-2">Create your account to begin your journey</p>
        </div>

        <div className="p-6 space-y-6">
          <div className="space-y-4">
            <button
              onClick={handleGoogleSignIn}
              disabled={isLoading}
              className="w-full flex items-center justify-center gap-3 bg-white text-gray-800 py-3 px-4 rounded-md hover:bg-gray-100 transition-colors font-medium"
            >
              {isLoading ? (
                <svg className="animate-spin h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4" />
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
                </svg>
              )}
              <span>{isLoading ? 'Signing in...' : 'Continue with Google'}</span>
            </button>

            <div className="relative flex items-center justify-center">
              <div className="border-t border-gray-800 flex-grow"></div>
              <span className="mx-4 text-sm text-text-secondary">or</span>
              <div className="border-t border-gray-800 flex-grow"></div>
            </div>

            <div className="text-center py-6">
              <p className="text-text-secondary mb-2">Coming soon:</p>
              <div className="flex justify-center gap-4">
                <button disabled className="w-10 h-10 bg-background border border-gray-800 rounded-md flex items-center justify-center text-text-secondary opacity-50 cursor-not-allowed">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"/>
                  </svg>
                </button>
                <button disabled className="w-10 h-10 bg-background border border-gray-800 rounded-md flex items-center justify-center text-text-secondary opacity-50 cursor-not-allowed">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                  </svg>
                </button>
                <button disabled className="w-10 h-10 bg-background border border-gray-800 rounded-md flex items-center justify-center text-text-secondary opacity-50 cursor-not-allowed">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M12.225 12.225h-1.778V9.44c0-.664-.012-1.519-.925-1.519-.926 0-1.068.724-1.068 1.47v2.834H6.676V6.498h1.707v.783h.024c.348-.594.996-.95 1.684-.925 1.802 0 2.135 1.185 2.135 2.728l-.001 3.14zM4.67 5.715a1.037 1.037 0 01-1.032-1.031c0-.566.466-1.032 1.032-1.032.566 0 1.031.466 1.032 1.032 0 .566-.466 1.032-1.032 1.032zm.889 6.51h-1.78V6.498h1.78v5.727zM13.11 2H2.885A.88.88 0 002 2.866v10.268a.88.88 0 00.885.866h10.226a.882.882 0 00.889-.866V2.865a.88.88 0 00-.889-.864z"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div className="text-center text-sm text-text-secondary">
            <p>By signing up, you agree to our</p>
            <div className="mt-1">
              <Link href="/terms" className="text-primary hover:underline">Terms of Service</Link>
              <span> and </span>
              <Link href="/privacy" className="text-primary hover:underline">Privacy Policy</Link>
            </div>
          </div>
        </div>

        <div className="bg-background p-4 border-t border-gray-800 text-center">
          <p className="text-sm text-text-secondary">
            Already have an account? <Link href="/" className="text-primary hover:underline">Sign in</Link>
          </p>
        </div>
      </div>
    </main>
  )
}