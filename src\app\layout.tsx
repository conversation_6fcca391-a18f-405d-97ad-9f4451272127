import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import Header from '@/components/Header'
import Providers from '@/components/Providers'
import GoogleAnalytics from '@/components/GoogleAnalytics'
import { AnalyticsConsentBanner } from '@/components/AnalyticsConsent'
import { env } from '@/lib/env'

const inter = Inter({ subsets: ['latin'] })

// Validate environment variables during server rendering
const envValidation = env.validate()
if (!envValidation.isValid) {
  console.error(`
⚠️ Missing environment variables: ${envValidation.missing.join(', ')}
Please check your .env.local file and restart the server.
  `)
}

export const metadata: Metadata = {
  title: 'SoloQuest - Single Player Roleplaying Experience',
  description: 'An AI-powered single-player Dungeons & Dragons experience with immersive storytelling.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="icon" type="image/svg+xml" href="/images/d20-icon.svg" />
        <link rel="shortcut icon" href="/images/d20-icon.svg" />
      </head>
      <body className={`${inter.className} min-h-screen`} suppressHydrationWarning>
        <Providers>
          <GoogleAnalytics />
          <Header />
          <main className="container mx-auto px-4 py-8">
            {children}
          </main>
          <footer className="bg-background-darker py-6 mt-12 border-t border-gray-800">
            <div className="container mx-auto px-4 text-center text-text-secondary">
              <p className="medieval-text">SoloQuest - An Immersive Single Player Experience</p>
              <p className="text-sm mt-2">© {new Date().getFullYear()} SoloQuest Project</p>
            </div>
          </footer>
          <AnalyticsConsentBanner />
        </Providers>
      </body>
    </html>
  )
}
